# Projects Management Guide

This guide explains how to add, modify, and manage projects in your portfolio.

## Project Structure

Each project follows this interface:

```typescript
interface Project {
  id: string                    // Unique identifier (auto-generated from title)
  title: string                 // Project name
  description: string           // Detailed description
  images: string[]             // Array of image paths
  tags: string[]               // Technologies used
  category: string             // "Web Application", "Mobile Application", etc.
  github?: string              // GitHub repository URL (optional)
  liveLink?: string            // Live demo URL (optional)
  featured?: boolean           // Show as featured project (optional)
  url?: string                 // URL for browser frame display (web projects only)
  status?: string              // "completed", "in-progress", "planned" (optional)
  startDate?: string           // Project start date (optional)
  endDate?: string             // Project end date (optional)
}
```

## Adding New Projects

### Method 1: Direct Addition to projects.ts

1. Open `src/data/projects.ts`
2. Add your project to the `projects` array:

```typescript
{
  id: "my-awesome-project", // Will be auto-generated if using createNewProject
  title: "My Awesome Project",
  description: "A detailed description of what this project does and the problems it solves.",
  images: [
    "/images/projects/my-project/screenshot1.jpg",
    "/images/projects/my-project/screenshot2.jpg",
  ],
  tags: ["React", "TypeScript", "Tailwind CSS", "Next.js"],
  category: "Web Application", // or "Mobile Application"
  github: "https://github.com/yourusername/my-project",
  liveLink: "https://my-project.vercel.app",
  featured: true,
  url: "my-project.vercel.app", // For web projects (shows in browser frame)
  status: "completed"
}
```

### Method 2: Using the Helper Function

```typescript
import { createNewProject } from './projects'

const newProject = createNewProject({
  title: "My New Project",
  description: "Project description...",
  images: ["/images/projects/new-project/image1.jpg"],
  tags: ["React", "Node.js"],
  category: "Web Application",
  featured: false
})

// Then add to the projects array
```

## Project Categories

Current supported categories:
- **Web Application**: Uses BrowserFrame component
- **Mobile Application**: Uses PhoneFrame component

You can add new categories by simply using them in your projects.

## Image Management

1. Create a folder for your project: `/public/images/projects/your-project-name/`
2. Add your screenshots/images to this folder
3. Reference them in the images array: `"/images/projects/your-project-name/image.jpg"`

### Recommended Image Specifications

**Web Applications:**
- Resolution: 1920x1080 or 1440x900
- Format: JPG or PNG
- File size: < 500KB for better loading

**Mobile Applications:**
- Resolution: 375x812 (iPhone) or 360x640 (Android)
- Format: JPG or PNG
- File size: < 300KB

## Frame Components

### BrowserFrame
Used for web applications. Supports:
- Custom URL display
- Light/dark themes
- Show/hide browser controls

### PhoneFrame
Used for mobile applications. Supports:
- iPhone/Android models
- Custom status bar time
- Show/hide status bar
- Modal/regular sizes

## Best Practices

1. **Use descriptive project IDs**: They should be URL-friendly
2. **Optimize images**: Compress images for web use
3. **Write clear descriptions**: Explain the problem solved and technologies used
4. **Use relevant tags**: Help with filtering and categorization
5. **Set appropriate URLs**: For web projects, use clean domain names
6. **Mark featured projects**: Highlight your best work

## Example: Complete Project Entry

```typescript
{
  id: "ecommerce-dashboard",
  title: "E-commerce Admin Dashboard",
  description: "A comprehensive admin dashboard for managing e-commerce operations. Features real-time analytics, inventory management, order processing, and customer support tools. Built with modern React patterns and optimized for performance.",
  images: [
    "/images/projects/ecommerce-dashboard/dashboard-overview.jpg",
    "/images/projects/ecommerce-dashboard/analytics-page.jpg",
    "/images/projects/ecommerce-dashboard/inventory-management.jpg",
    "/images/projects/ecommerce-dashboard/order-processing.jpg"
  ],
  tags: ["React", "TypeScript", "Tailwind CSS", "Chart.js", "React Query", "Zustand"],
  category: "Web Application",
  github: "https://github.com/yourusername/ecommerce-dashboard",
  liveLink: "https://ecommerce-dashboard.vercel.app",
  featured: true,
  url: "ecommerce-dashboard.vercel.app",
  status: "completed",
  startDate: "2024-01",
  endDate: "2024-03"
}
```

## Troubleshooting

**Images not loading?**
- Check file paths are correct
- Ensure images are in the `/public` directory
- Verify image file extensions match

**Project not showing?**
- Check the project is added to the `projects` array
- Verify all required fields are present
- Check for TypeScript errors

**Frame not displaying correctly?**
- For web projects: Ensure `url` field is set
- For mobile projects: Use "Mobile Application" category
- Check image aspect ratios match frame expectations
