"use client"

import type React from "react"

import { useState } from "react"
import { Github, X, ExternalLink, ChevronLeft, ChevronRight, Monitor, Smartphone } from "lucide-react"
import BrowserFrame from "./ui/BrowserFrame"
import PhoneFrame from "./ui/PhoneFrame"
import { projects, getCategories, getProjectsByCategory, type Project } from "../data/projects"

const ProjectsSection = () => {
  const [activeFilter, setActiveFilter] = useState<string>("All")
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0)

  const categories = getCategories()
  const filteredProjects = getProjectsByCategory(activeFilter)

  const openProjectModal = (project: Project) => {
    setSelectedProject(project)
    setCurrentImageIndex(0)
    document.body.style.overflow = "hidden"
  }

  const closeProjectModal = () => {
    setSelectedProject(null)
    setCurrentImageIndex(0)
    document.body.style.overflow = "unset"
  }

  const nextImage = () => {
    if (selectedProject) {
      setCurrentImageIndex((prev) => (prev + 1) % selectedProject.images.length)
    }
  }

  const prevImage = () => {
    if (selectedProject) {
      setCurrentImageIndex((prev) => (prev - 1 + selectedProject.images.length) % selectedProject.images.length)
    }
  }

  const isMobileApp = (category: string) => category.toLowerCase().includes("mobile")

  return (
    <>
      <section id="projects" className="py-8 md:py-16 bg-gradient-to-br from-muted/30 to-muted/60">
        <div className="container px-4 md:px-6">
          {/* Header */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Featured Projects
            </h2>
            <p className="text-muted-foreground mb-6 md:mb-8 max-w-2xl mx-auto text-base md:text-lg px-4">
              {projects.length > 0
                ? "Discover my latest work and the technologies I've been exploring. Each project tells a story of problem-solving and innovation."
                : "This section will showcase my projects and development work."}
            </p>
          </div>

          {/* Category Filter */}
          {projects.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 md:gap-3 mb-8 md:mb-12 px-4">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveFilter(category)}
                  className={`px-4 md:px-6 py-2 md:py-3 rounded-full text-xs md:text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                    activeFilter === category
                      ? "bg-primary text-primary-foreground shadow-lg shadow-primary/25"
                      : "bg-background text-foreground hover:bg-secondary border border-border hover:border-primary/30"
                  }`}
                >
                  {category}
                  {category !== "All" && (
                    <span className="ml-2 px-2 py-0.5 bg-primary/10 text-primary text-xs rounded-full">
                      {projects.filter((p) => p.category === category).length}
                    </span>
                  )}
                </button>
              ))}
            </div>
          )}

          {/* Projects Grid */}
          {projects.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8">
              {filteredProjects.map((project, index) => (
                <div
                  key={project.title}
                  onClick={() => openProjectModal(project)}
                  className="group cursor-pointer bg-background rounded-xl border border-border overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  {/* Featured Badge */}
                  {project.featured && (
                    <div className="absolute top-3 left-3 z-10 px-2 py-1 bg-primary text-primary-foreground text-xs font-medium rounded-md">
                      Featured
                    </div>
                  )}

                  {/* Project Preview with Frame */}
                  <div className="relative h-80 md:h-96 overflow-hidden bg-gradient-to-br from-muted/50 to-muted p-4 md:p-6 flex items-center justify-center">
                    {isMobileApp(project.category) ? (
                      <PhoneFrame className="h-full max-h-[350px]">
                        <img
                          src={project.images[0] || "/placeholder.svg"}
                          alt={project.title}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                      </PhoneFrame>
                    ) : (
                      <BrowserFrame className="h-full w-full max-w-[400px]" url={project.url}>
                        <img
                          src={project.images[0] || "/placeholder.svg"}
                          alt={project.title}
                          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        />
                      </BrowserFrame>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>

                  {/* Content */}
                  <div className="p-4 md:p-5">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-base md:text-lg font-semibold group-hover:text-primary transition-colors line-clamp-1">
                        {project.title}
                      </h3>
                      <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-md whitespace-nowrap ml-2">
                        {isMobileApp(project.category) ? <Smartphone size={12} /> : <Monitor size={12} />}
                        <span className="hidden sm:inline">{project.category}</span>
                      </div>
                    </div>

                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2 leading-relaxed">
                      {project.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {project.tags.slice(0, 3).map((tag, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                      {project.tags.length > 3 && (
                        <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                          +{project.tags.length - 3}
                        </span>
                      )}
                    </div>

                    {/* Quick Actions */}
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      {project.github && <span>• GitHub</span>}
                      {project.liveLink && <span>• Live Demo</span>}
                      <span className="ml-auto">Click to view</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            /* Enhanced Empty State */
            <div className="text-center py-12 md:py-20">
              <div className="max-w-md mx-auto px-4">
                <div className="relative mb-8">
                  <div className="w-24 md:w-32 h-24 md:h-32 mx-auto bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center">
                    <Github size={32} className="text-primary md:w-12 md:h-12" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 md:w-8 h-6 md:h-8 bg-primary/10 rounded-full animate-pulse"></div>
                  <div className="absolute -bottom-2 -left-2 w-4 md:w-6 h-4 md:h-6 bg-primary/20 rounded-full animate-pulse delay-300"></div>
                </div>
                <h3 className="text-xl md:text-2xl font-bold mb-4">No Projects Yet</h3>
                <p className="text-muted-foreground mb-6 md:mb-8 leading-relaxed">
                  Di ko pa nalalagay mga projects ko dito. Check back soon for exciting updates!
                </p>
                <div className="flex justify-center gap-4">
                  <div className="w-12 md:w-16 h-2 bg-muted rounded-full animate-pulse"></div>
                  <div className="w-8 md:w-12 h-2 bg-muted rounded-full animate-pulse delay-150"></div>
                  <div className="w-16 md:w-20 h-2 bg-muted rounded-full animate-pulse delay-300"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Project Modal */}
      {selectedProject && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-2 md:p-4 bg-black/80 backdrop-blur-sm">
          <div className="relative w-full max-w-7xl h-full max-h-[95vh] md:max-h-[90vh] bg-background rounded-lg md:rounded-2xl overflow-hidden shadow-2xl">
            {/* Close Button */}
            <button
              onClick={closeProjectModal}
              className="absolute top-2 md:top-4 right-2 md:right-4 z-10 p-2 bg-background/80 backdrop-blur-sm rounded-full hover:bg-background transition-colors"
            >
              <X size={20} />
            </button>

            <div className="flex flex-col md:flex-row h-full">
              {/* Project Preview in Modal */}
              <div className="flex-1 relative bg-gradient-to-br from-muted/30 to-muted/60 flex items-center justify-center p-4 md:p-8">
                {selectedProject.images.length > 0 && (
                  <>
                    <div className="relative max-w-full max-h-full flex items-center justify-center">
                      {isMobileApp(selectedProject.category) ? (
                        <PhoneFrame isModal={true} className="h-full max-h-[700px] md:max-h-[800px]">
                          <img
                            src={selectedProject.images[currentImageIndex] || "/placeholder.svg"}
                            alt={`${selectedProject.title} - ${currentImageIndex + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </PhoneFrame>
                      ) : (
                        <BrowserFrame className="max-w-full max-h-full" url={selectedProject.url}>
                          <img
                            src={selectedProject.images[currentImageIndex] || "/placeholder.svg"}
                            alt={`${selectedProject.title} - ${currentImageIndex + 1}`}
                            className="w-full h-auto max-h-[400px] md:max-h-[500px] object-contain"
                          />
                        </BrowserFrame>
                      )}
                    </div>

                    {/* Image Navigation */}
                    {selectedProject.images.length > 1 && (
                      <>
                        <button
                          onClick={prevImage}
                          className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 p-2 md:p-3 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                        >
                          <ChevronLeft size={16} className="md:w-5 md:h-5" />
                        </button>
                        <button
                          onClick={nextImage}
                          className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 p-2 md:p-3 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                        >
                          <ChevronRight size={16} className="md:w-5 md:h-5" />
                        </button>

                        {/* Image Counter */}
                        <div className="absolute bottom-2 md:bottom-4 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-black/50 text-white text-sm rounded-full">
                          {currentImageIndex + 1} / {selectedProject.images.length}
                        </div>
                      </>
                    )}
                  </>
                )}
              </div>

              {/* Project Details */}
              <div className="w-full md:w-80 lg:w-96 bg-background border-t md:border-t-0 md:border-l border-border p-4 md:p-6 overflow-y-auto">
                <div className="space-y-4 md:space-y-6">
                  {/* Project Title */}
                  <div>
                    <h2 className="text-xl md:text-2xl font-bold mb-2">{selectedProject.title}</h2>
                    <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full w-fit">
                      {isMobileApp(selectedProject.category) ? <Smartphone size={14} /> : <Monitor size={14} />}
                      {selectedProject.category}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    {selectedProject.github && (
                      <a
                        href={selectedProject.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors text-sm font-medium"
                      >
                        <Github size={16} />
                        GitHub
                      </a>
                    )}
                    {selectedProject.liveLink && (
                      <a
                        href={selectedProject.liveLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
                      >
                        <ExternalLink size={16} />
                        {isMobileApp(selectedProject.category) ? "Download" : "View Live"}
                      </a>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <p className="text-muted-foreground leading-relaxed text-sm md:text-base">
                      {selectedProject.description}
                    </p>
                  </div>

                  {/* Technologies */}
                  <div>
                    <h3 className="text-base md:text-lg font-semibold mb-3">Technologies</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedProject.tags.map((tag, idx) => (
                        <span
                          key={idx}
                          className="px-3 py-1 bg-secondary text-secondary-foreground text-sm rounded-md font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ProjectsSection
