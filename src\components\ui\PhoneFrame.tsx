import type React from "react"

interface PhoneFrameProps {
  children: React.ReactNode
  className?: string
  isModal?: boolean
  model?: "iphone" | "android"
  showStatusBar?: boolean
  time?: string
}

const PhoneFrame: React.FC<PhoneFrameProps> = ({
  children,
  className = "",
  isModal = false,
  model = "iphone",
  showStatusBar = true,
  time = "9:41"
}) => {
  const phoneWidth = isModal ? "280px" : "220px"
  const phoneHeight = isModal ? "560px" : "440px"

  return (
    <div className={`relative mx-auto ${className}`} style={{ width: phoneWidth, height: phoneHeight }}>
      {/* Phone Shadow */}
      <div className="absolute inset-0 bg-black/30 rounded-[3.5rem] blur-2xl transform translate-y-4 scale-105"></div>

      {/* Phone Outer Frame */}
      <div className="relative bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 rounded-[3.5rem] p-1 h-full shadow-2xl border-2 border-gray-700">
        {/* Phone Inner Frame */}
        <div className="bg-black rounded-[3rem] h-full relative overflow-hidden">
          {/* Dynamic Island / Notch */}
          {model === "iphone" && (
            <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-black rounded-full z-30 border border-gray-800 shadow-inner">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center gap-3">
                {/* Camera */}
                <div className="w-2.5 h-2.5 bg-gray-900 rounded-full border border-gray-700"></div>
                {/* Speaker */}
                <div className="w-8 h-1 bg-gray-900 rounded-full"></div>
              </div>
            </div>
          )}

          {/* Status Bar */}
          {showStatusBar && (
            <div className="absolute top-0 left-0 right-0 z-20 bg-white px-6 pt-8 pb-2 flex justify-between items-center text-black text-sm font-semibold">
              <span>{time}</span>
              <div className="flex items-center gap-1">
                {/* WiFi */}
                <svg className="w-4 h-4 ml-1" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.07 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z" />
                </svg>
                {/* Battery */}
                <div className="w-6 h-3 border border-black rounded-sm ml-1 relative">
                  <div className="w-4 h-1.5 bg-green-500 rounded-sm m-0.5"></div>
                  <div className="absolute -right-0.5 top-1 w-0.5 h-1 bg-black rounded-r-sm"></div>
                </div>
              </div>
            </div>
          )}

          {/* App Content */}
          <div className={`${showStatusBar ? 'pt-16' : 'pt-8'} h-full bg-white overflow-hidden`}>
            {children}
          </div>
          
          {/* Home Indicator */}
          {model === "iphone" && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-36 h-1.5 bg-white/40 rounded-full"></div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PhoneFrame
