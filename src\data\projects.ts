export interface Project {
  id: string
  title: string
  description: string
  images: string[]
  tags: string[]
  category: string
  github?: string
  liveLink?: string
  featured?: boolean
  url?: string // For browser frame display
  status?: "completed" | "in-progress" | "planned"
  startDate?: string
  endDate?: string
}

export const projects: Project[] = [
  {
    id: "sz-hotpot-haven",
    title: "S&Z Hot Pot Haven",
    description:
      "A modern web application for S&Z Hot Pot Haven, a premium hotpot ingredient store located in Bacoor, Cavite. This website allows customers to browse products, add items to cart, and place orders online with an intuitive interface designed for both customers and administrators.",
    images: [
      "/images/projects/szhotpot/szhotpot1.jpg",
      "/images/projects/szhotpot/szhotpot2.jpg",
      "/images/projects/szhotpot/szhotpot3.jpg",
      "/images/projects/szhotpot/szhotpot4.jpg",
      "/images/projects/szhotpot/szhotpot5.jpg",
      "/images/projects/szhotpot/szhotpot6.jpg",
    ],
    tags: ["PHP", "MySQL", "HTML5", "CSS3", "JavaScript", "Bootstrap"],
    category: "Web Application",
    liveLink: "https://szhotpot.free.nf/",
    github: "https://github.com/0phl/sz-hotpot-store",
    featured: true,
    url: "szhotpot.free.nf",
    status: "completed",
    startDate: "2024-01",
    endDate: "2024-03"
  },
  {
    id: "mobile-app-project",
    title: "Mobile App Project",
    description:
      "A modern mobile application built with React Native. This app features a clean, intuitive interface with smooth animations and excellent user experience. Perfect for on-the-go users who need quick access to essential features.",
    images: [
      "/images/projects/szhotpot/szmobile.jpg",
      "/placeholder.svg?height=600&width=300",
      "/placeholder.svg?height=600&width=300",
    ],
    tags: ["React Native", "TypeScript", "Expo", "Firebase", "Redux"],
    category: "Mobile Application",
    liveLink: "https://play.google.com/store",
    github: "https://github.com/yourusername/mobile-app",
    featured: false,
    status: "in-progress",
    startDate: "2024-02"
    // Mobile apps don't need URL since they use PhoneFrame
  }
]

// Helper functions for project management
export const getFeaturedProjects = (): Project[] => {
  return projects.filter(project => project.featured)
}

export const getProjectsByCategory = (category: string): Project[] => {
  if (category === "All") return projects
  return projects.filter(project => project.category === category)
}

export const getProjectById = (id: string): Project | undefined => {
  return projects.find(project => project.id === id)
}

export const getCategories = (): string[] => {
  return ["All", ...Array.from(new Set(projects.map(project => project.category)))]
}

// Template for adding new projects
export const createNewProject = (projectData: Omit<Project, 'id'>): Project => {
  return {
    id: projectData.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
    ...projectData
  }
}
